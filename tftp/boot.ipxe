#!ipxe

# boot.ipxe - served via HTTP

# Set the IP of your HTTP server (the Docker host)
set http-server *************

# Set a menu timeout
set menu-timeout 5000

:menu
menu PXE Boot Menu (sanboot over HTTP)

item --key 1 arch    Boot Arch Linux ISO
item --key 2 gparted Boot GParted Live ISO
item --key s shell   Drop to iPXE shell
choose --timeout ${menu-timeout} --default arch selected
goto ${selected}

:arch
echo Booting Arch Linux...
# sanboot can directly boot many modern ISOs over HTTP
sanboot --no-describe http://${http-server}/isos/archlinux-2025.07.01-x86_64.iso || goto menu

:gparted
echo Booting GParted Live...
sanboot --no-describe http://${http-server}/isos/gparted-live-1.6.0-5-amd64.iso || goto menu

:shell
shell
goto menu
