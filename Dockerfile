# Use the official Alpine Linux base image
FROM alpine:latest

# Install dnsmasq and nginx for HTTPS support
RUN apk update && \
  apk add --no-cache dnsmasq nginx openssl

# Generate self-signed SSL certificate
RUN openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout /etc/nginx/nginx.key \
  -out /etc/nginx/nginx.crt \
  -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"

# Copy dnsmasq configuration
COPY dnsmasq.conf /etc/dnsmasq.conf

# Copy TFTP assets
COPY var/tftp /var/tftp
RUN chmod -R 755 /var/tftp

# Copy website assets to match nginx.conf and docker-compose volume
COPY www /srv/pxe
RUN chown -R nginx:nginx /srv/pxe

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy the entrypoint script and make it executable
COPY entrypoint.sh /
RUN chmod +x /entrypoint.sh

# Expose required ports (good practice, though --net=host is used)
EXPOSE 67/udp 69/udp 80/tcp 443/tcp

# Set the entrypoint to our script. This is the "exec" form.
ENTRYPOINT ["/entrypoint.sh"]
