#! /usr/bin/env bash

docker build -t pxe-docker .

docker run -it --rm --name pxe-server \
  --net=host \
  --cap-add=NET_ADMIN \
  pxe-docker

sudo lsof -iUDP -nP | grep -E ':67|:53'

sudo netstat -tulnp | grep -E ':67|:53'

sudo netstat -tulnp | grep -E 'dnsmasq|nginx'

sudo tcpdump -ni any port 67 or port 4011 or port 69

qemu-system-x86_64 -netdev user,id=net0 -device e1000,netdev=net0 \
  -boot n -nographic

wget https://boot.ipxe.org/ipxe.iso
qemu-system-x86_64 -cdrom ipxe.iso -boot d -m 512

# tftp <pxe-ip>
# tftp> get undionly.kpxe
# tftp> quit

curl -k https://************/boot.ipxe

# iPXE> chain https://************/boot.ipxe

curl -k https://************/

# # on another machine
# sudo apt install atftp
# atftp ************
# atftp >get undionly.kpxe

qemu-system-x86_64 -m 512 -netdev user,id=n1 -device e1000,netdev=n1 -cdrom ipxe.iso

sudo setenforce 0

# Example for /var/tftp or /var/www/html
sudo chcon -Rt httpd_sys_content_t /var/tftp

# make it persistent
sudo semanage fcontext -a -t httpd_sys_content_t "/var/tftp(/.*)?"
sudo restorecon -Rv /var/tftp

sudo setenforce 1
