# pxe-docker

This project provides a simple PXE (Preboot Execution Environment) server setup using Docker. It includes DNSMasq for DHCP/TFTP and Nginx for serving files over HTTP.

## Prerequisites

- Docker
- `qemu-system-x86_64` for testing (optional)
- `tcpdump` and `netstat` for debugging (optional)

## Building the Docker Image

To build the Docker image, run the following command:

```bash
docker build -t pxe-docker .
```

## Running the Container

To run the PXE server container:

```bash
docker run -it --rm --name pxe-server \
  --net=host \
  --cap-add=NET_ADMIN \
  pxe-docker
```

This will run the container with host networking and the `NET_ADMIN` capability, which is required for `dnsmasq` to function correctly.

## Testing

### Testing with QEMU

You can test the PXE boot process using QEMU.

To boot a VM and have it PXE boot from the server:
```bash
qemu-system-x86_64 -netdev bridge,id=net0,br=br0 -device e1000,netdev=net0 \
  -boot n -nographic
```

Alternatively, you can use an iPXE ISO to test:
```bash
# Download the iPXE iso
wget https://boot.ipxe.org/ipxe.iso

# Boot QEMU with the iPXE iso
qemu-system-x86_64 -cdrom ipxe.iso -boot d -m 512
```
Once in the iPXE shell, you can chainload the boot script from your PXE server:
```
iPXE> chain https://<your-server-ip>/boot.ipxe
```

### Testing Services

You can test if the TFTP and HTTP servers are working correctly.

**TFTP:**
```bash
# On another machine on the same network
sudo apt install atftp
atftp <pxe-server-ip>
atftp> get undionly.kpxe
```
or
```bash
tftp <pxe-server-ip>
tftp> get undionly.kpxe
tftp> quit
```

**HTTP:**
```bash
curl -k http://<pxe-server-ip>/boot.ipxe
```

## Debugging

Here are some commands to help debug the PXE server.

### Check listening ports

To see which processes are listening on the necessary ports (UDP 67 for DHCP, 53 for DNS, TCP 80/443 for HTTP).

```bash
sudo lsof -iUDP -nP | grep -E ':67|:53'
sudo netstat -tulnp | grep -E ':67|:53'
sudo netstat -tulnp | grep -E 'dnsmasq|nginx'
```

### Sniffing Network Traffic

You can use `tcpdump` to see the PXE boot traffic (DHCP, TFTP).

```bash
sudo tcpdump -ni any port 67 or port 4011 or port 69
```

## SELinux

If you are running on a system with SELinux enabled (like CentOS or Fedora), you might need to adjust the security contexts of the `tftp` and `www` directories if you are mounting them from your host.

Temporarily disable SELinux:
```bash
  sudo setenforce 0
```

To set the correct file contexts:
```bash
# Example for /var/tftp or your local tftp directory
sudo chcon -Rt httpd_sys_content_t /path/to/your/tftp
```

To make the context changes persistent:
```bash
sudo semanage fcontext -a -t httpd_sys_content_t "/path/to/your/tftp(/.*)?"
sudo restorecon -Rv /path/to/your/tftp
```

Re-enable SELinux:
```bash
sudo setenforce 1
```
