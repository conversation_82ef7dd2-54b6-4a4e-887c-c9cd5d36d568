# /etc/dnsmasq.conf -- Revised for clarity and robustness

# General Settings
port=0 # Disable DNS
dhcp-no-override

log-dhcp

# TFTP Settings
enable-tftp
tftp-root=/var/tftp

# DHCP Proxy Settings
# This server will only respond to PXE requests, not assign IPs.
# Replace with your Docker host's static IP.
dhcp-range=***********,proxy

# === PXE Boot Logic ===

# This is the key. When iPXE starts, it identifies itself with user-class "iPXE".
# We set a tag 'ipxe' for any client that does this.
dhcp-userclass=set:ipxe,iPXE

# Stage 1: For any client that is NOT tagged 'ipxe' (the initial boot ROM),
# use pxe-service to offer the iPXE bootloader binaries based on architecture.
pxe-service=tag:!ipxe,x86PC,"Loading iPXE (BIOS)",undionly.kpxe
pxe-service=tag:!ipxe,BC_EFI,"Loading iPXE (UEFI)",ipxe.efi
pxe-service=tag:!ipxe,X86-64_EFI,"Loading iPXE (UEFI)",ipxe.efi

# Stage 2: For any client that IS tagged 'ipxe', there is no pxe-service.
# This forces it to use the dhcp-boot option below.
# Here we serve the boot script via TFTP first, then HTTP for ISOs.
dhcp-boot=tag:ipxe,boot.ipxe,************
