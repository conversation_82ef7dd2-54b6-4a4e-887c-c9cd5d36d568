#!ipxe

# IP of the PXE HTTP server
set http-server ************
set menu-timeout 5000

:menu
menu PXE Boot Menu (HTTP Sanboot)

item --key 1 arch    Boot Arch Linux ISO
item --key 2 gparted Boot GParted Live ISO
item --key s shell   Drop to iPXE shell
choose --timeout ${menu-timeout} --default arch selected || goto menu
goto ${selected}

:arch
echo Booting Arch Linux via sanboot...
sanboot --no-describe http://${http-server}/isos/archlinux-2025.07.01-x86_64.iso || goto menu

:gparted
echo Booting GParted Live via sanboot...
sanboot --no-describe http://${http-server}/isos/gparted-live-1.6.0-5-amd64.iso || goto menu

:shell
echo Dropping to iPXE shell...
shell
goto menu
