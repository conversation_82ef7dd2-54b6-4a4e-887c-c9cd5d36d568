#!ipxe

echo iPXE boot script loaded successfully!
echo Network configuration:
ifstat
echo
echo Starting in 3 seconds...
sleep 3

:menu
menu PXE Boot Menu
item --key 1 arch    Boot Arch Linux ISO
item --key 2 gparted Boot GParted Live ISO
item --key s shell   Drop to iPXE shell
item --key r reboot  Reboot
choose --default arch --timeout 10000 selected || goto shell
goto ${selected}

:arch
echo Booting Arch Linux...
sanboot --no-describe http://************/isos/archlinux-2025.07.01-x86_64.iso || goto menu

:gparted
echo Booting GParted Live...
sanboot --no-describe http://************/isos/gparted-live-1.6.0-5-amd64.iso || goto menu

:shell
echo Dropping to iPXE shell...
shell

:reboot
reboot
